import { Injectable } from '@nestjs/common'
import { PrismaService } from '../prisma/prisma.service'

@Injectable()
export class SlogansService {
  constructor(private prisma: PrismaService) {}

  async findVisible() {
    // Only return slogans with isVisible = true
    // Used in mobile app
    // Using type assertion to avoid TypeScript errors
    return await this.prisma.slogan.findMany({
      where: {
        isVisible: true,
      } as any,
    })
  }
}
