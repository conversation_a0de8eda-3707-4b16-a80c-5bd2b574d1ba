import { Injectable, NotFoundException, ConflictException } from '@nestjs/common'
import { PrismaService } from '../prisma/prisma.service'
import { CreateFoodDto, UpdateFoodDto } from './food.dto'
import { Prisma } from '@prisma/client'

@Injectable()
export class FoodsService {
  constructor(private prisma: PrismaService) {}

  async create(createFoodDto: CreateFoodDto) {
    try {
      return await this.prisma.food.create({
        data: {
          name: createFoodDto.name,
          price: createFoodDto.price,
          category: createFoodDto.category,
          isAvailable: createFoodDto.isAvailable ?? true,
        },
      })
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Food name already exists')
        }
      }
      throw error
    }
  }

  async findAll() {
    return await this.prisma.food.findMany()
  }

  async findOne(id: string) {
    const food = await this.prisma.food.findUnique({
      where: { id },
    })

    if (!food) {
      throw new NotFoundException(`Food with ID ${id} not found`)
    }

    return food
  }

  async update(id: string, updateFoodDto: UpdateFoodDto) {
    try {
      await this.findOne(id) // Check if food exists

      const updateData: any = {}
      if (updateFoodDto.name !== undefined) updateData.name = updateFoodDto.name
      if (updateFoodDto.price !== undefined) updateData.price = updateFoodDto.price
      if (updateFoodDto.category !== undefined) updateData.category = updateFoodDto.category
      if (updateFoodDto.isAvailable !== undefined) updateData.isAvailable = updateFoodDto.isAvailable

      return await this.prisma.food.update({
        where: { id },
        data: updateData,
      })
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException(`Food with ID ${id} not found`)
        }
        if (error.code === 'P2002') {
          throw new ConflictException('Food name already exists')
        }
      }
      throw error
    }
  }

  async remove(id: string) {
    try {
      await this.findOne(id) // Check if food exists
      return await this.prisma.food.delete({
        where: { id },
      })
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException(`Food with ID ${id} not found`)
        }
      }
      throw error
    }
  }
}
